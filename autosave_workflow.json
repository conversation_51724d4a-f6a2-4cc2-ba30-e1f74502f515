{"vid": "048D", "pid": "7632", "steps": [{"template_path": "autosave_workflow_assets\\step_1_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Left Click", "key_to_press": "", "template_name": "录制模板 1", "step_interval": 1.0}, {"template_path": "autosave_workflow_assets\\step_2_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Press Key", "key_to_press": "l", "template_name": "录制模板 2", "step_interval": 1.28}, {"template_path": "autosave_workflow_assets\\step_3_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Press Key", "key_to_press": "u", "template_name": "录制模板 3", "step_interval": 0.29}, {"template_path": "autosave_workflow_assets\\step_4_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Press Key", "key_to_press": "space", "template_name": "录制模板 4", "step_interval": 0.62}, {"template_path": "autosave_workflow_assets\\step_5_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Press Key", "key_to_press": "z", "template_name": "录制模板 5", "step_interval": 2.22}, {"template_path": "autosave_workflow_assets\\step_6_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Press Key", "key_to_press": "i", "template_name": "录制模板 6", "step_interval": 0.15}, {"template_path": "autosave_workflow_assets\\step_7_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Press Key", "key_to_press": "space", "template_name": "录制模板 7", "step_interval": 0.96}, {"template_path": "autosave_workflow_assets\\step_8_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Left Click", "key_to_press": "", "template_name": "录制模板 8", "step_interval": 2.0}, {"template_path": "autosave_workflow_assets\\step_9_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Left Click", "key_to_press": "", "template_name": "录制模板 9", "step_interval": 1.36}, {"template_path": "autosave_workflow_assets\\step_10_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Left Click", "key_to_press": "", "template_name": "录制模板 10", "step_interval": 2.0}, {"template_path": "autosave_workflow_assets\\step_11_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Left Click", "key_to_press": "", "template_name": "录制模板 1", "step_interval": 49.07}, {"template_path": "autosave_workflow_assets\\step_12_template.png", "offset_x": 0, "offset_y": 0, "action_type": "Right Click", "key_to_press": "", "template_name": "录制模板 2", "step_interval": 0.4}, {"template_path": "autosave_workflow_assets\\step_13_template.png", "offset_x": -5, "offset_y": -5, "action_type": "Left Click", "key_to_press": "", "template_name": "录制模板 3", "step_interval": 5.0}]}